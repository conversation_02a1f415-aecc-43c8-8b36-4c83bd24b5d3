# UV Cache Management Guide / UV Cache 管理指南

## 📚 Available Languages / 可用語言

Choose your preferred language for the cache management guide:

### 🇺🇸 English
**[Cache Management Guide (English)](cache-management.en.md)**
- Complete guide for UV cache management
- Troubleshooting for Windows file lock issues
- Automated cleanup solutions

### 🇹🇼 繁體中文 (Traditional Chinese)
**[Cache 管理指南 (繁體中文)](cache-management.md)**
- 完整的 UV cache 管理指南
- Windows 檔案佔用問題解決方案
- 自動化清理解決方案

### 🇨🇳 简体中文 (Simplified Chinese)
**[Cache 管理指南 (简体中文)](cache-management.zh-CN.md)**
- 完整的 UV cache 管理指南
- Windows 文件占用问题解决方案
- 自动化清理解决方案

---

## 🚀 Quick Start / 快速開始

### Check Cache Size / 檢查 Cache 大小
```bash
python scripts/cleanup_cache.py --size
```

### Clean Cache / 清理 Cache
```bash
# Standard cleanup / 標準清理
python scripts/cleanup_cache.py --clean

# Force cleanup (for file lock issues) / 強制清理（解決檔案佔用問題）
python scripts/cleanup_cache.py --force
```

---

## 📋 Summary / 摘要

The UV cache can grow significantly (tens of GB) due to frequent `uvx` usage. This guide provides:

UV cache 可能因頻繁使用 `uvx` 而增長到數十 GB。本指南提供：

- ✅ **Smart cleanup tools** / **智能清理工具**
- ✅ **Windows file lock solutions** / **Windows 檔案佔用解決方案**
- ✅ **Automated monitoring** / **自動化監控**
- ✅ **Best practices** / **最佳實踐**
