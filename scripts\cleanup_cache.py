#!/usr/bin/env python3
"""
UV Cache 清理腳本
================

定期清理 uv cache 以防止磁碟空間不斷增加

使用方式：
  python scripts/cleanup_cache.py --dry-run    # 預覽將要清理的內容
  python scripts/cleanup_cache.py --clean      # 執行清理
  python scripts/cleanup_cache.py --size       # 查看 cache 大小
"""

import subprocess
import sys
import argparse
import shutil
from pathlib import Path
import os

def get_cache_dir():
    """取得 uv cache 目錄"""
    # Windows 預設路徑
    if os.name == 'nt':
        return Path.home() / "AppData" / "Local" / "uv"
    # macOS/Linux 預設路徑
    else:
        return Path.home() / ".cache" / "uv"

def get_cache_size(cache_dir):
    """計算 cache 目錄大小"""
    if not cache_dir.exists():
        return 0
    
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(cache_dir):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            try:
                total_size += os.path.getsize(filepath)
            except (OSError, FileNotFoundError):
                pass
    return total_size

def format_size(size_bytes):
    """格式化檔案大小顯示"""
    if size_bytes == 0:
        return "0 B"
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def run_uv_command(command, check=True):
    """執行 uv 命令"""
    try:
        result = subprocess.run(
            ["uv"] + command,
            capture_output=True,
            text=True,
            check=check
        )
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令執行失敗: uv {' '.join(command)}")
        print(f"錯誤: {e.stderr}")
        return None
    except FileNotFoundError:
        print("❌ 找不到 uv 命令，請確認 uv 已正確安裝")
        return None

def show_cache_info():
    """顯示 cache 資訊"""
    print("🔍 UV Cache 資訊")
    print("=" * 50)
    
    cache_dir = get_cache_dir()
    print(f"Cache 目錄: {cache_dir}")
    
    if cache_dir.exists():
        cache_size = get_cache_size(cache_dir)
        print(f"Cache 大小: {format_size(cache_size)}")
        
        # 顯示子目錄大小
        subdirs = []
        for subdir in cache_dir.iterdir():
            if subdir.is_dir():
                subdir_size = get_cache_size(subdir)
                subdirs.append((subdir.name, subdir_size))
        
        if subdirs:
            print("\n📁 子目錄大小:")
            subdirs.sort(key=lambda x: x[1], reverse=True)
            for name, size in subdirs[:10]:  # 顯示前10大
                print(f"  {name}: {format_size(size)}")
    else:
        print("Cache 目錄不存在")

def clean_cache(dry_run=False):
    """清理 cache"""
    action = "預覽" if dry_run else "執行"
    print(f"🧹 {action} UV Cache 清理")
    print("=" * 50)
    
    # 顯示清理前的大小
    cache_dir = get_cache_dir()
    if cache_dir.exists():
        before_size = get_cache_size(cache_dir)
        print(f"清理前大小: {format_size(before_size)}")
    else:
        print("Cache 目錄不存在，無需清理")
        return
    
    if dry_run:
        print("\n🔍 將要清理的內容:")
        # 使用 uv cache clean --dry-run (如果支援)
        result = run_uv_command(["cache", "clean", "--dry-run"], check=False)
        if result and result.returncode == 0:
            print(result.stdout)
        else:
            print("  - 所有 cache 內容")
    else:
        print("\n🗑️  正在清理...")
        # 執行清理
        result = run_uv_command(["cache", "clean"], check=False)
        if result and result.returncode == 0:
            print("✅ Cache 清理完成")
            
            # 顯示清理後的大小
            if cache_dir.exists():
                after_size = get_cache_size(cache_dir)
                saved_size = before_size - after_size
                print(f"清理後大小: {format_size(after_size)}")
                print(f"節省空間: {format_size(saved_size)}")
            else:
                print(f"節省空間: {format_size(before_size)}")
        else:
            print("❌ Cache 清理失敗")

def main():
    parser = argparse.ArgumentParser(description="UV Cache 清理工具")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--size", action="store_true", help="顯示 cache 大小資訊")
    group.add_argument("--dry-run", action="store_true", help="預覽清理內容（不實際清理）")
    group.add_argument("--clean", action="store_true", help="執行 cache 清理")
    
    args = parser.parse_args()
    
    if args.size:
        show_cache_info()
    elif args.dry_run:
        clean_cache(dry_run=True)
    elif args.clean:
        clean_cache(dry_run=False)

if __name__ == "__main__":
    main()
