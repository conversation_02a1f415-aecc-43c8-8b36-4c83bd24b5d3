# Release v2.2.1 - 視窗優化與統一設定接口

## 🌟 亮點
本版本主要解決了 GUI 視窗大小限制問題，實現了視窗狀態的智能保存機制，並優化了設定接口的統一性。

## 🚀 改進功能
- 🖥️ **視窗大小限制解除**: 解除 GUI 主視窗最小大小限制，從 1000×800 降至 400×300，讓用戶可以自由調整視窗大小以符合不同使用場景
- 💾 **視窗狀態實時保存**: 實現視窗大小與位置的即時保存機制，支援防抖延遲避免過度頻繁的 I/O 操作
- ⚙️ **統一設定接口優化**: 改進 GUI 設定版面的配置保存邏輯，避免設定衝突，確保視窗定位與大小設定的正確性
- 🎯 **智能視窗大小保存**: 「總是在主螢幕中心顯示」模式下正確保存視窗大小（但不保存位置），「智能定位」模式下保存完整的視窗狀態

## 🐛 問題修復
- 🔧 **視窗大小限制**: 解決 GUI 視窗無法調整至小尺寸的問題 (fixes #10 第一部分)
- 🛡️ **設定衝突**: 修復設定保存時可能出現的配置衝突問題

## 📦 安裝與更新
```bash
# 快速測試最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.1 test
```

## 🔗 相關連結
- 完整文檔: [README.zh-TW.md](../../README.zh-TW.md)
- 問題回報: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 解決問題: #10 (部分完成) 