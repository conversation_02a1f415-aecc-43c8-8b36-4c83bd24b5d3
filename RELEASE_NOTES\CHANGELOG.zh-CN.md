# 更新日志 (简体中文)

本文件记录了 **MCP Feedback Enhanced** 的所有版本更新内容。

---
# Release v2.2.2 - 超时自动清理修复

## 🌟 亮点
本版本修复了一个重要的资源管理问题：当 MCP session 因超时结束时，GUI/Web UI 界面没有正确关闭，导致界面持续显示而无法正常关闭。

## 🐛 问题修复
- 🔄 **超时自动清理**: 修复 GUI/Web UI 在 MCP session timeout (默认 600 秒) 后没有自动关闭的问题
- 🛡️ **资源管理优化**: 改进超时处理机制，确保在超时时正确清理和关闭所有 UI 资源
- ⚡ **超时检测增强**: 加强超时检测逻辑，确保在各种情况下都能正确处理超时事件
- 🔧 **界面响应改进**: 改善 Web UI 前端对 session timeout 事件的处理响应

## 🚀 技术改进
- 📦 **Web Session 管理**: 重构 WebFeedbackSession 的超时处理逻辑
- 🎯 **QTimer 整合**: 在 GUI 中引入精确的 QTimer 超时控制机制
- 🌐 **前端通信优化**: 改进 Web UI 前端与后端的超时消息传递
- 🧹 **资源清理机制**: 新增 _cleanup_resources_on_timeout 方法确保彻底清理

## 📦 安装与更新
```bash
# 快速测试最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.2 test
```

## 🔗 相关链接
- 完整文档: [README.zh-CN.md](../../README.zh-CN.md)
- 问题报告: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 解决问题: #5 (GUI/Web UI timeout cleanup) 
---

## [v2.2.2] - 超时自动清理修复 (2024-12-XX)

### 🌟 亮点
本版本修复了一个重要的资源管理问题：当 MCP session 因超时结束时，GUI/Web UI 界面没有正确关闭，导致界面持续显示而无法正常关闭。

### 🐛 问题修复
- 🔄 **超时自动清理**: 修复 GUI/Web UI 在 MCP session timeout (默认 600 秒) 后没有自动关闭的问题 (fixes #5)
- 🛡️ **资源管理优化**: 改进超时处理机制，确保在超时时正确清理和关闭所有 UI 资源
- ⚡ **超时检测增强**: 加强超时检测逻辑，确保在各种情况下都能正确处理超时事件
- 🔧 **界面响应改进**: 改善 Web UI 前端对 session timeout 事件的处理响应

### 🚀 技术改进
- 📦 **Web Session 管理**: 重构 WebFeedbackSession 的超时处理逻辑
- 🎯 **QTimer 整合**: 在 GUI 中引入精确的 QTimer 超时控制机制
- 🌐 **前端通信优化**: 改进 Web UI 前端与后端的超时消息传递
- 🧹 **资源清理机制**: 新增 _cleanup_resources_on_timeout 方法确保彻底清理

---

## [v2.2.1] - 窗口优化与统一设置接口 (2024-12-XX)

### 🌟 亮点
本版本主要解决了 GUI 窗口大小限制问题，实现了窗口状态的智能保存机制，并优化了设置接口的统一性。

### 🚀 改进功能
- 🖥️ **窗口大小限制解除**: 解除 GUI 主窗口最小大小限制，从 1000×800 降至 400×300，让用户可以自由调整窗口大小以符合不同使用场景
- 💾 **窗口状态实时保存**: 实现窗口大小与位置的即时保存机制，支持防抖延迟避免过度频繁的 I/O 操作
- ⚙️ **统一设置接口优化**: 改进 GUI 设置版面的配置保存逻辑，避免设置冲突，确保窗口定位与大小设置的正确性
- 🎯 **智能窗口大小保存**: 「总是在主屏幕中心显示」模式下正确保存窗口大小（但不保存位置），「智能定位」模式下保存完整的窗口状态

### 🐛 问题修复
- 🔧 **窗口大小限制**: 解决 GUI 窗口无法调整至小尺寸的问题 (fixes #10 第一部分)
- 🛡️ **设置冲突**: 修复设置保存时可能出现的配置冲突问题

---

## [v2.2.0] - 布局与设置界面优化 (2024-12-XX)

### 🌟 亮点
本版本新增了水平布局选项，优化了设置界面，并修复了快捷键和图片粘贴问题。

### ✨ 新功能
- 🎨 **水平布局模式**: GUI 与 Web UI 的合并模式新增摘要与反馈的左右布局（水平分割）选项，提供更灵活的查看方式 (实现 [Issue #1](https://github.com/Minidoracat/mcp-feedback-enhanced/issues/1))

### 🚀 改进功能
- 🎨 **设置界面改进**: 优化了 GUI 与 Web UI 的设置页面，提升布局清晰度与用户操作体验
- ⌨️ **快捷键完善 (GUI)**: 提交反馈快捷键 (Ctrl+Enter / Cmd+Enter) 现已完整支持数字键盘(九宫格)的 Enter 键

### 🐛 问题修复
- 🔧 **图片重复粘贴 (Web UI)**: 解决了在文本输入区使用 Ctrl+V 粘贴图片时，可能导致图片重复粘贴的问题

---

## [v2.1.1] - 窗口定位优化 (2024-11-XX)

### 🌟 亮点
完美解决多屏幕环境下的窗口定位问题，特别是 T 字型屏幕排列等复杂配置。

### ✨ 新功能
- 🖥️ **智能窗口定位**: 新增「总是在主屏幕中心显示窗口」设置选项
- 🌐 **多屏幕支持**: 完美解决 T 字型屏幕排列等复杂多屏幕环境的窗口定位问题
- 💾 **位置记忆**: 自动保存和恢复窗口位置，智能检测窗口可见性
- ⚙️ **用户选择**: 提供智能定位（默认）和强制中心显示两种模式

---

## [v2.1.0] - 全面重构版 (2024-11-XX)

### 🌟 亮点
这是一个重大重构版本，GUI 和 Web UI 均采用了全新的模块化架构。

### 🎨 重大重构
- 🏗️ **全面重构**: GUI 和 Web UI 采用模块化架构
- 📁 **集中管理**: 重新组织文件夹结构，提升维护性
- 🖥️ **界面优化**: 现代化设计和改进的用户体验

### ✨ 新功能
- 🍎 **macOS 界面优化**: 针对 macOS 用户体验进行专项改进
- ⚙️ **功能增强**: 新增设置选项和自动关闭页面功能
- ℹ️ **关于页面**: 新增关于页面，包含版本信息、项目链接和致谢内容

### 🐛 问题修复
- 🌐 **语言切换**: 修复 Web UI 语言切换时内容更新问题

---

## [v2.0.14] - 快捷键与图片功能增强 (2024-10-XX)

### 🚀 改进功能
- ⌨️ **增强快捷键**: Ctrl+Enter 支持数字键盘
- 🖼️ **智能图片粘贴**: Ctrl+V 直接粘贴剪贴板图片

---

## [v2.0.9] - 多语言架构重构 (2024-10-XX)

### 🔄 重构
- 🌏 **多语言架构重构**: 支持动态载入
- 📁 **语言文件模块化**: 模块化组织语言文件

---

## [v2.0.3] - 编码问题修复 (2024-10-XX)

### 🐛 重要修复
- 🛡️ **完全修复中文字符编码问题**: 解决所有中文显示相关问题
- 🔧 **解决 JSON 解析错误**: 修复数据解析错误

---

## [v2.0.0] - Web UI 支持 (2024-09-XX)

### 🌟 重大功能
- ✅ **新增 Web UI 支持**: 支持远程环境使用
- ✅ **自动环境检测**: 自动选择合适的界面
- ✅ **WebSocket 即时通讯**: 实现即时双向通讯

---

## 图例说明

| 图标 | 意义 |
|------|------|
| 🌟 | 版本亮点 |
| ✨ | 新功能 |
| 🚀 | 改进功能 |
| 🐛 | 问题修复 |
| 🔄 | 重构变更 |
| 🎨 | 界面优化 |
| ⚙️ | 设置相关 |
| 🖥️ | 窗口相关 |
| 🌐 | 多语言/网络相关 |
| 📁 | 文件结构 |
| ⌨️ | 快捷键 |
| 🖼️ | 图片功能 |

---

**完整项目信息：** [GitHub - mcp-feedback-enhanced](https://github.com/Minidoracat/mcp-feedback-enhanced) 