/**
 * Web UI 樣式
 * ===========
 * 
 * 補充樣式和動畫效果
 */

/* 連接狀態指示器 */
.connection-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.connection-indicator.connected {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
    border: 1px solid #4caf50;
}

.connection-indicator.disconnected {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
    border: 1px solid #f44336;
}

/* 載入動畫 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #464647;
    border-radius: 50%;
    border-top-color: #007acc;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 淡入動畫 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 滑入動畫 */
.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 脈衝動畫 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 滾動條美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #606060;
}

/* 選擇文字顏色 */
::selection {
    background: rgba(0, 122, 204, 0.3);
    color: var(--text-primary);
}

/* 無障礙改進 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦點可見性 */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* 禁用狀態 */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 響應式圖片 */
img {
    max-width: 100%;
    height: auto;
}

/* 表格樣式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
}

tr:hover {
    background: rgba(255, 255, 255, 0.02);
}

/* 代碼區塊 */
code {
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
}

pre {
    background: var(--bg-tertiary);
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    border: 1px solid var(--border-color);
}

pre code {
    background: none;
    padding: 0;
}

/* 警告和提示框 */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    border-left: 4px solid;
}

.alert-info {
    background: rgba(33, 150, 243, 0.1);
    border-left-color: var(--info-color);
    color: #bbdefb;
}

.alert-success {
    background: rgba(76, 175, 80, 0.1);
    border-left-color: var(--success-color);
    color: #c8e6c9;
}

.alert-warning {
    background: rgba(255, 152, 0, 0.1);
    border-left-color: var(--warning-color);
    color: #ffe0b2;
}

.alert-error {
    background: rgba(244, 67, 54, 0.1);
    border-left-color: var(--error-color);
    color: #ffcdd2;
}

/* 進度條 */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

/* 分隔線 */
.divider {
    height: 1px;
    background: var(--border-color);
    margin: 20px 0;
}

/* 徽章 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 12px;
    background: var(--accent-color);
    color: white;
}

.badge-secondary {
    background: var(--text-secondary);
}

.badge-success {
    background: var(--success-color);
}

.badge-warning {
    background: var(--warning-color);
}

.badge-error {
    background: var(--error-color);
}

/* 卡片 */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.card-body {
    color: var(--text-secondary);
}

/* 統計數字 */
.stat {
    text-align: center;
    padding: 20px;
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: var(--accent-color);
    display: block;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9em;
    margin-top: 4px;
} 